import factory
from factory.django import DjangoModelFactory
from organizations.models import Organization

class OrganizationFactory(DjangoModelFactory):
    class Meta:
        model = Organization
        django_get_or_create = ('external_id',)

    external_id = factory.Sequence(lambda n: n)
    settings = factory.LazyAttribute(lambda o: {"name": f"Test Organization {o.external_id}"})
    workspaces_data = {}

    @factory.post_generation
    def roles(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for role in extracted:
                self.roles.add(role)
