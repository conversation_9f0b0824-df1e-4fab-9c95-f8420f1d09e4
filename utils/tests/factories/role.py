import factory
from factory.django import DjangoModelFactory
from gabbro.acl.models import Role

class RoleFactory(DjangoModelFactory):
    class Meta:
        model = Role
        django_get_or_create = ('codename',)

    title = factory.Sequence(lambda n: f'Test Role {n}')
    codename = factory.Sequence(lambda n: f'test_role_{n}')

    @factory.post_generation
    def permissions(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for permission in extracted:
                self.permissions.add(permission)
