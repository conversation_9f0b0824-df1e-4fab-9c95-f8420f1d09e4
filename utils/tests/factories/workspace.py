import factory
from factory.django import DjangoModelFactory
from workspaces.models import Workspace
from .user import UserFactory
from .organization import OrganizationFactory

class WorkspaceFactory(DjangoModelFactory):
    class Meta:
        model = Workspace

    name = factory.Sequence(lambda n: f'Test Workspace {n}')
    description = factory.Faker('text', max_nb_chars=200)
    owner = factory.SubFactory(UserFactory)
    organization = factory.SubFactory(OrganizationFactory)
    layers_data = {
        'layers_count': 0,
        'records_count': 0
    }
    workspace_type = 'REGULAR'

    @factory.post_generation
    def users(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for user in extracted:
                self.acl_add_user(user)

    @factory.post_generation
    def roles(self, create, extracted, **kwargs):
        if not create:
            return
        if extracted:
            for role in extracted:
                self.acl_add_role(role)
