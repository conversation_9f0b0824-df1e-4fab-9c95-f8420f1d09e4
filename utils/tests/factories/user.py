import factory
from factory.django import DjangoModelFactory
from django.contrib.auth import get_user_model
from users.models import ActiveStatusChoices

User = get_user_model()

class UserFactory(DjangoModelFactory):
    class Meta:
        model = User
        django_get_or_create = ('email',)

    email = factory.Sequence(lambda n: f'user{n}@example.com')
    phone = factory.Sequence(lambda n: f'+123456789{n:02d}')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    is_staff = False
    is_superuser = False
    active_status = ActiveStatusChoices.ACTIVE

    @factory.post_generation
    def password(self, create, extracted, **kwargs):
        if not create:
            return
        password = extracted or 'testpass123'
        self.set_password(password)
        if create:
            self.save()

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        organization = kwargs.pop("organization", None)
        roles = kwargs.pop("roles", None)
        user = super(UserFactory, cls)._create(model_class, *args, **kwargs)
        organization.acl_add_user(user, roles)
        return user
