import graphene
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest

from common.utils import (
    PageInfo,
    DjangoFilterInput,
    authentication_required,
    filter_qs_paginate_with_count,
    build_q,
    build_advanced_q,
    organization_required,
    authorize_multiple_objects_for_user,
    FilterGroupInput,
)
from layers.models import Layer
from layers.schema.object_types import LayerListType, RecordListType
from organizations.perms_constants import VIEW_WORKSPACE
from workspaces.models import Workspace


class Query(graphene.ObjectType):
    layers = graphene.Field(
        LayerListType,
        org_id=graphene.Int(required=True),
        workspace_id=graphene.Int(required=True),
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
        filter_groups=graphene.List(FilterGroupInput),
    )
    records = graphene.Field(
        RecordListType,
        org_id=graphene.Int(required=True),
        layer_id=graphene.Int(required=True),
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
        filter_groups=graphene.List(FilterGroupInput),
        auto_complete=graphene.Boolean(default_value=False),
    )

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_layers(
        root,
        info,
        org_id: int,
        workspace_id,
        pk=None,
        page_info=None,
        filters=None,
        filter_groups=None,
        **kwargs,
    ):
        user = info.context.user
        organization = info.context.organization
        workspace: Workspace = Workspace.objects.filter(
            organization=organization, id=workspace_id
        ).first()
        if not workspace:
            raise BadRequest(reason={"workspace_id": _("Invalid workspace_id") % {}})

        authorize_multiple_objects_for_user(
            models_objs=[organization, workspace], perm=VIEW_WORKSPACE, user=user
        )
        queryset = workspace.layers.all()

        if filter_groups:
            q_obj = build_advanced_q(filter_groups, pk)
        else:
            q_obj = build_q(pk, filters)

        return LayerListType(*filter_qs_paginate_with_count(queryset, q_obj, page_info))

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_records(
        root,
        info,
        layer_id,
        auto_complete,
        pk=None,
        page_info=None,
        filters=None,
        filter_groups=None,
        **kwargs,
    ):
        user = info.context.user
        organization = info.context.organization
        layer = Layer.objects.filter(
            workspace__organization=organization, id=layer_id
        ).first()
        if not layer:
            raise BadRequest(reason={"layer_id": _("Invalid layer_id") % {}})

        authorize_multiple_objects_for_user(
            models_objs=[organization, layer.workspace], perm=VIEW_WORKSPACE, user=user
        )
        records = layer.records.all()

        if filter_groups:
            q_obj = build_advanced_q(filter_groups, pk)
            if auto_complete:
                fields = [
                    filter_item["field"]
                    for group in filter_groups
                    for filter_item in group["filters"]
                ]
                records = records.distinct(*fields)
        else:
            q_obj = build_q(pk, filters)
        return RecordListType(*filter_qs_paginate_with_count(records, q_obj, page_info))
