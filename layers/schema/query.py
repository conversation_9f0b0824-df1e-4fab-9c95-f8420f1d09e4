import graphene
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest

from common.utils import (
    PageInfo,
    DjangoFilterInput,
    authentication_required,
    filter_qs_paginate_with_count,
    build_q,
    organization_required,
    authorize_multiple_objects_for_user,
)
from layers.models import Layer
from layers.schema.object_types import LayerListType, RecordListType
from organizations.perms_constants import VIEW_WORKSPACE
from workspaces.models import Workspace


class Query(graphene.ObjectType):
    layers = graphene.Field(
        LayerListType,
        org_id=graphene.Int(required=True),
        workspace_id=graphene.Int(required=True),
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    records = graphene.Field(
        RecordListType,
        org_id=graphene.Int(required=True),
        layer_id=graphene.Int(required=True),
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_layers(
        root,
        info,
        org_id: int,
        workspace_id,
        pk=None,
        page_info=None,
        filters=None,
        **kwargs,
    ):
        user = info.context.user
        organization = info.context.organization
        workspace: Workspace = Workspace.objects.filter(
            organization=organization, id=workspace_id
        ).first()
        if not workspace:
            raise BadRequest(reason={"workspace_id": _("Invalid workspace_id") % {}})

        authorize_multiple_objects_for_user(
            models_objs=[organization, workspace], perm=VIEW_WORKSPACE, user=user
        )
        queryset = workspace.layers.all()
        return LayerListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_records(
        root, info, layer_id, pk=None, page_info=None, filters=None, **kwargs
    ):
        user = info.context.user
        organization = info.context.organization
        layer = Layer.objects.filter(
            workspace__organization=organization, id=layer_id
        ).first()
        if not layer:
            raise BadRequest(reason={"layer_id": _("Invalid layer_id") % {}})

        authorize_multiple_objects_for_user(
            models_objs=[organization, layer.workspace], perm=VIEW_WORKSPACE, user=user
        )
        queryset = layer.records.all()
        return RecordListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )
