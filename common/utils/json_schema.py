from collections import defaultdict

import jsonschema
from django.core.exceptions import ValidationError
from genson.schema.builder import SchemaBuilder
from jsonschema import exceptions


class SchemaBuilderWithEnum(SchemaBuilder):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.enums = defaultdict(set)

    def add_object(self, obj):
        # Process the object and collect unique values
        super().add_object(obj)
        for key, value in obj.items():
            if isinstance(value, (list, tuple)):
                self.enums[key].update(value)
            elif isinstance(value, dict):
                continue
            else:
                self.enums[key].add(value)

    def to_schema(self):
        schema = super().to_schema()
        schema["definitions"] = {}

        # Add enums or const to the definitions and reference them in properties
        for key, values in self.enums.items():
            # values = sorted(values)
            if 1 < len(values) < 20:
                # Use "enum" for multi-value enums
                values = list(values)
                schema["definitions"][key] = {
                    "type": get_json_schema_type(values),
                    "enum": values,
                }
                # Reference the definition in the properties
                schema["properties"][key]["$ref"] = f"#/definitions/{key}"

        return schema


def get_json_schema_type(values):
    """Returns the JSON Schema type for each value in the list."""
    type_mapping = {
        str: "string",
        int: "integer",
        float: "number",
        bool: "boolean",
        list: "array",
        dict: "object",
        type(None): "null",
    }
    types = {type_mapping.get(type(value), "unknown") for value in values}
    types.discard("null")
    types = list(types)
    return types[0] if len(types) == 1 else types


def get_predicted_jsonschema(data: list[dict], with_enums: bool):
    builder = SchemaBuilderWithEnum() if with_enums else SchemaBuilder()
    for record in data:
        builder.add_object(record)
    return builder.to_schema()


def form_schema_validate(form_schema: dict, form_data: dict):
    try:
        jsonschema.validate(instance=form_data, schema=form_schema)
    except jsonschema.ValidationError as js_error:
        raise ValidationError(
            message={"json_schema": f"JsonSchema Validation Error: {js_error.message}"}
        )


def draft7_validate(form_schema, form_data, field_name="data"):
    # data field validation using jsonschema validator
    v = jsonschema.Draft7Validator(form_schema)
    errors = sorted(v.iter_errors(form_data), key=lambda e: e.path)
    validator_errors = [err.message for err in errors]
    if validator_errors:
        raise ValidationError({field_name: validator_errors})


def get_json_schema_errors(schema: dict):
    """
    Checks if a JSON Schema dictionary is valid.

    :param schema: The JSON Schema dictionary to validate.
    :return: True if the schema is valid, otherwise False.
    """
    try:
        jsonschema.Draft7Validator.check_schema(schema)
    except exceptions.SchemaError as error:
        return error.message


def extract_json_schema_top_level_fields(schema: dict) -> list:
    return list(schema.get("properties", {}).keys())


def extract_fields_from_json_schema(schema: dict) -> list[tuple[str, str]]:
    """
    Extract a list of fields with their types from a JSON Schema.
    """
    valid_types = {"string", "integer", "number", "boolean"}
    field_list = []
    properties = schema.get("properties", {})

    for field, details in properties.items():
        field_type = details.get("type")
        valid_type = None
        if isinstance(field_type, str) and field_type in valid_types:
            valid_type = field_type
        elif isinstance(field_type, list):
            valid_type = next((t for t in field_type if t in valid_types), None)

        if valid_type:
            field_list.append(dict(field=field, field_type=valid_type))

    return field_list
