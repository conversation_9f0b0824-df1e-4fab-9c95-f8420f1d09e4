# Introduced shared utility functions
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest

from common.utils import authorize_multiple_objects_for_user
from organizations.models import Organization
from organizations.perms_constants import VIEW_WORKSPACE
from users.models import User
from workspaces.models import Workspace


def validate_workspace_and_permissions(
    input_workspace_id: int, organization: Organization, user: User
) -> Workspace:
    # Find the workspace associated with the provided workspace ID
    workspace = Workspace.objects.filter(
        organization=organization, id=input_workspace_id
    ).first()
    if not workspace:
        raise BadRequest(reason={"workspace_id": _("Invalid workspace_id") % {}})
    # Ensure the user has permissions for both the organization and the workspace
    authorize_multiple_objects_for_user(
        models_objs=[organization, workspace], perm=VIEW_WORKSPACE, user=user
    )
    return workspace


def filter_layers(workspace: Workspace, layers_ids: list[int]):
    # Get all layers in the workspace
    layers = workspace.layers.all()
    # If layer IDs are provided, filter the layers by those IDs
    if layers_ids:
        layers = layers.filter(id__in=layers_ids)
        # If no layers match the provided IDs, raise a "BadRequest" exception
        if not layers:
            raise BadRequest(
                reason={"layers_ids": _("No layers found with the provided IDs") % {}}
            )
    return layers
