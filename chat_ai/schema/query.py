import graphene
from django.db.models import Count

from chat_ai.models import Conversation
from chat_ai.schema import validate_workspace_and_permissions, filter_layers
from chat_ai.schema.object_types import MessageListType
from common.utils import (
    authentication_required,
    PageInfo,
    DjangoFilterInput,
    filter_qs_paginate_with_count,
    build_q,
    organization_required,
)


class Query(graphene.ObjectType):
    messages = graphene.Field(
        MessageListType,
        org_id=graphene.Int(required=True),
        workspace_id=graphene.Int(required=True),
        layers_ids=graphene.List(graphene.Int),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_messages(
        root,
        info,
        workspace_id: int,
        layers_ids: list = None,
        page_info=None,
        filters=None,
        **kwargs,
    ):
        user = info.context.user
        organization = info.context.organization
        # Workspace validation and permission check
        workspace = validate_workspace_and_permissions(workspace_id, organization, user)
        # Process layers
        layers = filter_layers(workspace, layers_ids)

        conversation: Conversation = (
            Conversation.objects.annotate(layers_count=Count("layers", distinct=True))
            .filter(
                user=user,
                layers__id__in=layers.values_list("id", flat=True),
                layers_count=len(layers),
            )
            .order_by("-created")
            .first()
        )
        if not conversation:
            return MessageListType(count=0, data=[])

        queryset = conversation.messages.all()
        data, count = filter_qs_paginate_with_count(
            qs=queryset, q=build_q(pk=None, filters=filters), page_info=page_info
        )
        return MessageListType(count=count, data=data)
