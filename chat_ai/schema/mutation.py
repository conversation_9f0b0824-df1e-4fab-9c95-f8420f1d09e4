import graphene
from django.conf import settings
from django.db.models import Count
from gabbro.graphene.exceptions import BadRequest
from graphene_gis.scalars import JSONScalar

from chat_ai.ai_moduls.chat_gpt import ChatGPT, num_tokens_from_string
from chat_ai.models import Conversation
from chat_ai.schema.input_object_types import (
    NewChatInputType,
    ResetNewChatInputType,
    QuestionTypeEnum,
)
from chat_ai.schema.utils import validate_workspace_and_permissions, filter_layers
from common.utils import (
    authentication_required,
    organization_required,
    authorize_user,
    authorize_multiple_objects_for_user,
)
from organizations.perms_constants import CHANGE_WORKSPACE
from users.models import User


class ChatAI(graphene.Mutation):
    class Arguments:
        input_form = NewChatInputType(required=True)

    response = graphene.String()
    db_result = graphene.Field(JSONScalar)

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, input_form):
        # Extract data from the `input_form`
        question = input_form["question"]
        question_type = input_form["question_type"]
        gpt_model = GPT_MODEL_MAPPER[question_type]

        # Retrieve the authenticated user and organization from the context
        user: User = info.context.user
        organization = info.context.organization

        # Workspace validation and permission check
        workspace = validate_workspace_and_permissions(
            input_form["workspace_id"], organization, user
        )
        # Process layers
        layers = filter_layers(workspace, input_form.get("layers_ids"))

        # Verify that the number of tokens in the question is within the user input limit
        if num_tokens_from_string(gpt_model, question) > settings.USER_INPUT_TOKENS:
            raise BadRequest(
                reason={"question": "your question is too long, try to short it"}
            )

        # Retrieve the most recent conversation that includes any of the selected layers for the user
        conversation = (
            Conversation.objects.annotate(layers_count=Count("layers", distinct=True))
            .filter(
                user=user,
                layers__id__in=layers.values_list("id", flat=True),
                layers_count=len(layers),
            )
            .order_by("-created")
            .first()
        )

        # Create a ChatGPT instance to process the question
        chat_gpt = ChatGPT(
            layers=layers,
            user=user,
            gpt_model=gpt_model,
            user_input=question,
            conversation_id=conversation.id if conversation else None,
        )
        # Execute the GPT model to get a response and database result
        response, db_result = chat_gpt.execute(question_type)
        return ChatAI(response=response, db_result=db_result)


class ResetChatAI(graphene.Mutation):
    success = graphene.Boolean()

    class Arguments:
        data_input = ResetNewChatInputType(required=True)

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        # Retrieve the authenticated user and organization from the context
        user: User = info.context.user
        organization = info.context.organization

        # Workspace validation and permission check
        workspace = validate_workspace_and_permissions(
            data_input["workspace_id"], organization, user
        )
        # Process layers
        layers = filter_layers(workspace, data_input.get("layers_ids"))

        # Check if there's already an empty conversation for this user
        empty_conversation = (
            Conversation.objects.annotate(layers_count=Count("layers", distinct=True))
            .filter(
                user=user,
                messages__isnull=True,
                layers__id__in=layers.values_list("id", flat=True),
                layers_count=len(layers),
            )
            .order_by("-created")
            .first()
        )
        if empty_conversation:
            # Add all the layers to the existing empty conversation
            empty_conversation.layers.add(*layers)
        else:
            # Create a new conversation and add all layers to it
            conversation = Conversation.objects.create(user=user)
            conversation.layers.add(*layers)

        return ResetChatAI(success=True)


class ChatAIMutation(graphene.ObjectType):
    send_message_chat_ai = ChatAI.Field()
    reset_chat_ai = ResetChatAI.Field()


GPT_MODEL_MAPPER = {
    QuestionTypeEnum.DATA.value: settings.POSTGRES_MODEL,
    QuestionTypeEnum.GIS.value: settings.GIS_MODEL,
    QuestionTypeEnum.DATA_ACTION.value: settings.DJANGO_MODEL,
    QuestionTypeEnum.GENERAL.value: settings.DJANGO_MODEL,
}
