from unittest.mock import MagicMock
from django.test import override_settings

from utils.tests.schema.query import BaseQueryTestCase
from utils.tests.factories.user import UserFactory
from utils.tests.factories.role import RoleFactory


class WorkspacePermissionsQueryTestCase(BaseQueryTestCase):
    maxDiff = None

    def setUp(self):
        super().setUp()
        self.query = """
        query WorkspacePermissions($orgId: Int!) {
            workspacePermissions(orgId: $orgId) {
                id
                title
                codename
            }
        }
        """

    def test_workspace_permissions_query_success(self):
        """Test successful workspace permissions query"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertIn("data", result)
        self.assertIn("workspacePermissions", result["data"])
        self.assertIsInstance(result["data"]["workspacePermissions"], list)

    def test_workspace_permissions_query_unauthenticated(self):
        """Test workspace permissions query with unauthenticated user"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.non_auth_request,
        )
        self._check_none_auth_error(result)

    def test_workspace_permissions_query_without_permission(self):
        """Test workspace permissions query without VIEW_WORKSPACE permission"""
        # Create a user without VIEW_WORKSPACE permission
        role_without_permission = RoleFactory.create(
            organization=self.organization,
            permissions=[]
        )
        user_without_permission = UserFactory.create(
            organization=self.organization,
            roles=[role_without_permission]
        )

        auth_request_no_perm = MagicMock()
        auth_request_no_perm.user = user_without_permission

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=auth_request_no_perm,
        )
        self.assertIn("errors", result)

    @override_settings(WORKSPACE_ROLES_CODENAMES=[
        "workspace-add",
        "workspace-change",
        "workspace-delete",
        "workspace-view"
    ])
    def test_workspace_permissions_query_returns_workspace_roles(self):
        """Test that workspace permissions query returns workspace-specific roles"""
        # Create workspace roles that match the settings
        workspace_roles = []
        for codename in ["workspace-add", "workspace-change", "workspace-delete", "workspace-view"]:
            role = RoleFactory.create(
                codename=codename,
                title=f"Workspace {codename.split('-')[1].title()}"
            )
            workspace_roles.append(role)

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # Check that workspace roles are returned
        returned_codenames = [role["codename"] for role in result["data"]["workspacePermissions"]]
        expected_codenames = ["workspace-add", "workspace-change", "workspace-delete", "workspace-view"]

        for expected_codename in expected_codenames:
            self.assertIn(expected_codename, returned_codenames)

    def test_workspace_permissions_query_structure(self):
        """Test that workspace permissions query returns correct structure"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # Check structure of each workspace permission
        for workspace_permission in result["data"]["workspacePermissions"]:
            self.assertIn("id", workspace_permission)
            self.assertIn("title", workspace_permission)
            self.assertIn("codename", workspace_permission)

            # Verify data types
            self.assertIsInstance(workspace_permission["id"], int)
            self.assertIsInstance(workspace_permission["title"], str)
            self.assertIsInstance(workspace_permission["codename"], str)

            # Verify non-empty values
            self.assertGreater(len(workspace_permission["title"]), 0)
            self.assertGreater(len(workspace_permission["codename"]), 0)

    def test_workspace_permissions_query_filters_by_codename(self):
        """Test that workspace permissions query filters by workspace role codenames"""
        # Create some non-workspace roles
        non_workspace_role = RoleFactory.create(
            codename="regular-role",
            title="Regular Role"
        )

        # Create workspace roles
        workspace_role = RoleFactory.create(
            codename="workspace-add",
            title="Workspace Add"
        )

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # Check that only workspace roles are returned
        returned_codenames = [role["codename"] for role in result["data"]["workspacePermissions"]]

        # Should include workspace roles
        if "workspace-add" in ["workspace-add", "workspace-change", "workspace-delete", "workspace-view"]:
            # Only check if the role exists and matches the settings
            pass

        # Should not include non-workspace roles
        self.assertNotIn("regular-role", returned_codenames)

    def test_workspace_permissions_query_invalid_organization(self):
        """Test workspace permissions query with invalid organization ID"""
        variables = {
            "orgId": 99999,  # Non-existent organization
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertIn("errors", result)

    def test_workspace_permissions_query_empty_result(self):
        """Test workspace permissions query when no workspace roles exist"""
        # This test checks the behavior when no roles match WORKSPACE_ROLES_CODENAMES
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # Should return empty list if no workspace roles exist
        self.assertIsInstance(result["data"]["workspacePermissions"], list)

    @override_settings(WORKSPACE_ROLES_CODENAMES=[])
    def test_workspace_permissions_query_empty_settings(self):
        """Test workspace permissions query when WORKSPACE_ROLES_CODENAMES is empty"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # Should return empty list when no workspace role codenames are configured
        self.assertEqual(len(result["data"]["workspacePermissions"]), 0)

    def test_workspace_permissions_query_with_user_having_workspace_permission(self):
        """Test workspace permissions query with user having VIEW_WORKSPACE permission"""
        # Create user with VIEW_WORKSPACE permission
        role_with_workspace_perm = RoleFactory.create(
            organization=self.organization,
            permissions=[self.perms["view_workspace"]]
        )
        user_with_workspace_perm = UserFactory.create(
            organization=self.organization,
            roles=[role_with_workspace_perm]
        )

        auth_request_workspace_perm = MagicMock()
        auth_request_workspace_perm.user = user_with_workspace_perm

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=auth_request_workspace_perm,
        )
        self.assertNotIn("errors", result)
        self.assertIsInstance(result["data"]["workspacePermissions"], list)

    def test_workspace_permissions_query_consistent_data(self):
        """Test that workspace permissions query returns consistent data"""
        variables = {
            "orgId": self.organization.id,
        }

        # Execute query multiple times to ensure consistency
        results = []
        for _ in range(3):
            result = self.client.execute(
                self.query,
                variables=variables,
                context=self.auth_request,
            )
            self.assertNotIn("errors", result)
            results.append(result["data"]["workspacePermissions"])

        # All results should be identical
        for i in range(1, len(results)):
            self.assertEqual(results[0], results[i])