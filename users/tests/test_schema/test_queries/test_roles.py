from unittest.mock import MagicMock
from django.test import override_settings

from utils.tests.schema.query import BaseQueryTestCase
from utils.tests.factories.user import UserFactory
from utils.tests.factories.role import RoleFactory


class RolesQueryTestCase(BaseQueryTestCase):
    maxDiff = None

    def setUp(self):
        super().setUp()
        self.query = """
        query Roles($orgId: Int!, $pk: Int, $pageInfo: PageInfoInput, $filters: [DjangoFilterInput]) {
            roles(orgId: $orgId, pk: $pk, pageInfo: $pageInfo, filters: $filters) {
                data {
                    id
                    title
                    codename
                    usersCount
                    permissions {
                        id
                        name
                        codename
                    }
                }
                count
            }
        }
        """

    def test_roles_query_success(self):
        """Test successful roles query with basic parameters"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertIn("data", result)
        self.assertIn("roles", result["data"])
        self.assertIsInstance(result["data"]["roles"]["data"], list)
        self.assertIsInstance(result["data"]["roles"]["count"], int)
        # Should have at least one role (the one created in setUp)
        self.assertGreaterEqual(len(result["data"]["roles"]["data"]), 1)

    def test_roles_query_unauthenticated(self):
        """Test roles query with unauthenticated user"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.non_auth_request,
        )
        self._check_none_auth_error(result)

    def test_roles_query_without_permission(self):
        """Test roles query without VIEW_ROLE permission"""
        # Create a user without VIEW_ROLE permission
        role_without_permission = RoleFactory.create(
            organization=self.organization,
            permissions=[]
        )
        user_without_permission = UserFactory.create(
            organization=self.organization,
            roles=[role_without_permission]
        )

        auth_request_no_perm = MagicMock()
        auth_request_no_perm.user = user_without_permission

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=auth_request_no_perm,
        )
        self.assertIn("errors", result)

    def test_roles_query_with_pk_filter(self):
        """Test roles query with specific role pk"""
        # Create additional role
        additional_role = RoleFactory.create(
            organization=self.organization,
            title="Test Role",
            permissions=[self.perms["view_organization"]]
        )
        self.organization.roles.add(additional_role)

        variables = {
            "orgId": self.organization.id,
            "pk": additional_role.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertEqual(len(result["data"]["roles"]["data"]), 1)
        self.assertEqual(result["data"]["roles"]["data"][0]["id"], additional_role.id)
        self.assertEqual(result["data"]["roles"]["data"][0]["title"], "Test Role")

    def test_roles_query_with_pagination(self):
        """Test roles query with pagination"""
        # Create multiple roles
        for i in range(5):
            role = RoleFactory.create(
                title=f"Test Role {i}",
                permissions=[self.perms["view_organization"]]
            )
            self.organization.roles.add(role)

        variables = {
            "orgId": self.organization.id,
            "pageInfo": {
                "limit": 2,
                "offset": 0
            }
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertLessEqual(len(result["data"]["roles"]["data"]), 2)
        self.assertGreaterEqual(result["data"]["roles"]["count"], 5)  # At least 5 roles created + existing ones

    def test_roles_query_with_filters(self):
        """Test roles query with Django filters"""
        # Create role with specific title
        test_role = RoleFactory.create(
            title="Unique Test Role",
            permissions=[self.perms["view_organization"]]
        )
        self.organization.roles.add(test_role)

        variables = {
            "orgId": self.organization.id,
            "filters": [
                {
                    "field": "title",
                    "value": "Unique Test Role",
                    "lookup": "exact"
                }
            ]
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertEqual(len(result["data"]["roles"]["data"]), 1)
        self.assertEqual(result["data"]["roles"]["data"][0]["title"], "Unique Test Role")

    def test_roles_query_excludes_main_owner_role(self):
        """Test that main owner role is excluded from results"""
        # Create main owner role
        main_owner_role = RoleFactory.create(
            codename="main-owner",
            title="Main Owner",
            permissions=[self.perms["view_organization"]]
        )
        self.organization.roles.add(main_owner_role)

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        role_codenames = [role["codename"] for role in result["data"]["roles"]["data"]]
        self.assertNotIn("main-owner", role_codenames)

    def test_roles_query_includes_permissions(self):
        """Test that roles query includes permissions data"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # Check that at least one role has permissions
        roles_with_permissions = [
            role for role in result["data"]["roles"]["data"]
            if role["permissions"]
        ]
        self.assertGreater(len(roles_with_permissions), 0)

        # Check permission structure
        for role in roles_with_permissions:
            for permission in role["permissions"]:
                self.assertIn("id", permission)
                self.assertIn("name", permission)
                self.assertIn("codename", permission)

    def test_roles_query_users_count(self):
        """Test that roles query includes correct users count"""
        # Create role and assign users to it
        test_role = RoleFactory.create(
            title="Role with Users",
            permissions=[self.perms["view_organization"]]
        )
        self.organization.roles.add(test_role)

        # Create users and assign to role
        for i in range(3):
            user = UserFactory.create(
                organization=self.organization,
                roles=[test_role]
            )

        variables = {
            "orgId": self.organization.id,
            "pk": test_role.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertEqual(len(result["data"]["roles"]["data"]), 1)
        self.assertEqual(result["data"]["roles"]["data"][0]["usersCount"], 3)

    def test_roles_query_invalid_organization(self):
        """Test roles query with invalid organization ID"""
        variables = {
            "orgId": 99999,  # Non-existent organization
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertIn("errors", result)

    def test_roles_query_empty_organization(self):
        """Test roles query with organization that has no roles"""
        # Create new organization without roles
        from utils.tests.factories.organization import OrganizationFactory
        empty_org = OrganizationFactory()

        # Create user with permission for this organization
        role_with_perm = RoleFactory.create(
            organization=empty_org,
            permissions=[self.perms["view_role"]]
        )
        user_with_perm = UserFactory.create(
            organization=empty_org,
            roles=[role_with_perm]
        )

        auth_request_empty_org = MagicMock()
        auth_request_empty_org.user = user_with_perm

        variables = {
            "orgId": empty_org.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=auth_request_empty_org,
        )
        self.assertNotIn("errors", result)
        # Should return the role we just created (minus main owner if any)
        self.assertGreaterEqual(len(result["data"]["roles"]["data"]), 0)