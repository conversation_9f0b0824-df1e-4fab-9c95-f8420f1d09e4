from unittest.mock import MagicMock, patch

from django.contrib.auth.models import AnonymousUser
from django.test import override_settings

from utils.tests.schema.query import BaseQueryTestCase
from utils.tests.factories.user import UserFactory
from utils.tests.factories.role import RoleFactory
from utils.tests.factories.workspace import WorkspaceFactory
from users.models import ActiveStatusChoices


class UsersQueryTestCase(BaseQueryTestCase):
    maxDiff = None

    def setUp(self):
        super().setUp()
        self.query = """
        query Users($orgId: Int!, $pk: Int, $workspaceId: Int, $excludeIndividuals: Boolean, $pageInfo: PageInfo, $filters: [DjangoFilterInput]) {
            users(orgId: $orgId, pk: $pk, workspaceId: $workspaceId, excludeIndividuals: $excludeIndividuals, pageInfo: $pageInfo, filters: $filters) {
                data {
                    id
                    firstName
                    lastName
                    email
                    phone
                    isSuperuser
                    isStaff
                    activeStatus
                    avatar
                    role {
                        id
                        title
                        codename
                        usersCount
                        permissions {
                            id
                            name
                            codename
                        }
                    }
                }
                count
            }
        }
        """

    def test_users_query_success(self):
        """Test successful users query with basic parameters"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertIn("data", result)
        self.assertIn("users", result["data"])
        self.assertIsInstance(result["data"]["users"]["data"], list)
        self.assertIsInstance(result["data"]["users"]["count"], int)

    def test_users_query_unauthenticated(self):
        """Test users query with unauthenticated user"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.non_auth_request,
        )
        # Check that there are errors and it's an authentication error
        self.assertIn("errors", result)
        self.assertIn("Unauthorized", str(result["errors"]))

    def test_users_query_without_permission(self):
        """Test users query without VIEW_USER permission"""
        # Create a user without VIEW_USER permission
        role_without_permission = RoleFactory.create(
            organization=self.organization,
            permissions=[]
        )
        user_without_permission = UserFactory.create(
            organization=self.organization,
            roles=[role_without_permission]
        )

        auth_request_no_perm = MagicMock()
        auth_request_no_perm.user = user_without_permission

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=auth_request_no_perm,
        )
        self.assertIn("errors", result)

    def test_users_query_with_pk_filter(self):
        """Test users query with specific user pk"""
        # Create additional user
        additional_user = UserFactory.create(
            organization=self.organization,
            roles=[self.role]
        )

        variables = {
            "orgId": self.organization.id,
            "pk": additional_user.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertEqual(len(result["data"]["users"]["data"]), 1)
        self.assertEqual(result["data"]["users"]["data"][0]["id"], additional_user.id)

    def test_users_query_with_workspace_filter(self):
        """Test users query filtered by workspace"""
        # Create workspace and add users to it
        workspace = WorkspaceFactory.create(
            organization=self.organization,
            owner=self.user
        )
        workspace_user = UserFactory.create(
            organization=self.organization,
            roles=[self.role]
        )
        workspace.acl_add_user(workspace_user, roles=[self.role])

        variables = {
            "orgId": self.organization.id,
            "workspaceId": workspace.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        # Should include workspace_user but exclude owner (filtered out in query)
        user_ids = [user["id"] for user in result["data"]["users"]["data"]]
        self.assertIn(workspace_user.id, user_ids)

    def test_users_query_exclude_individuals_from_workspace(self):
        """Test users query excluding individuals from workspace"""
        # Create workspace and add users to it
        workspace = WorkspaceFactory.create(
            organization=self.organization,
            owner=self.user
        )
        workspace_user = UserFactory.create(
            organization=self.organization,
            roles=[self.role]
        )
        workspace.acl_add_user(workspace_user, roles=[self.role])

        variables = {
            "orgId": self.organization.id,
            "workspaceId": workspace.id,
            "excludeIndividuals": True,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        # Should exclude workspace_user
        user_ids = [user["id"] for user in result["data"]["users"]["data"]]
        self.assertNotIn(workspace_user.id, user_ids)

    def test_users_query_with_pagination(self):
        """Test users query with pagination"""
        # Create multiple users
        for i in range(5):
            UserFactory.create(
                organization=self.organization,
                roles=[self.role]
            )

        variables = {
            "orgId": self.organization.id,
            "pageInfo": {
                "limit": 2,
                "offset": 0
            }
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertLessEqual(len(result["data"]["users"]["data"]), 2)
        self.assertGreaterEqual(result["data"]["users"]["count"], 5)  # At least 5 users created + existing ones

    def test_users_query_with_filters(self):
        """Test users query with Django filters"""
        # Create user with specific email
        test_user = UserFactory.create(
            organization=self.organization,
            roles=[self.role],
            email="<EMAIL>"
        )

        variables = {
            "orgId": self.organization.id,
            "filters": [
                {
                    "field": "email",
                    "value": "<EMAIL>",
                    "lookup": "exact"
                }
            ]
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertEqual(len(result["data"]["users"]["data"]), 1)
        self.assertEqual(result["data"]["users"]["data"][0]["email"], "<EMAIL>")

    def test_users_query_excludes_deleted_users(self):
        """Test that deleted users are excluded from results"""
        # Create deleted user
        deleted_user = UserFactory.create(
            organization=self.organization,
            roles=[self.role],
            active_status=ActiveStatusChoices.DELETED
        )

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        user_ids = [user["id"] for user in result["data"]["users"]["data"]]
        self.assertNotIn(deleted_user.id, user_ids)

    def test_users_query_invalid_organization(self):
        """Test users query with invalid organization ID"""
        variables = {
            "orgId": 99999,  # Non-existent organization
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertIn("errors", result)

    def test_users_query_invalid_workspace(self):
        """Test users query with invalid workspace ID"""
        variables = {
            "orgId": self.organization.id,
            "workspaceId": 99999,  # Non-existent workspace
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)  # Should not error, just return all org users

    @patch('common.utils.models.get_owner_user')
    def test_users_query_no_owner_user(self, mock_get_owner_user):
        """Test users query when no owner user is found"""
        mock_get_owner_user.return_value = None

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertIn("errors", result)
        self.assertIn("No owner user found", str(result["errors"]))