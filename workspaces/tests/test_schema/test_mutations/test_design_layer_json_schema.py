import json
import os

from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils.translation import gettext_lazy as _

from app.settings import BASE_DIR
from common.tests.factories import BaseTestMixin, UserFactory, WorkspaceRequestFactory
from layers.models import Layer
from workspaces.models import (
    WorkspaceRequestChoices,
    RequestTypeChoices,
    Workspace,
)

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestDesignLayerJsonSchema(BaseTestMixin):
    def setUp(self):
        super().setUp()
        # Create a workspace request to use in the tests
        self.workspace_request = WorkspaceRequestFactory(
            created_by=self.user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            request_type=RequestTypeChoices.DESIGN_LAYER,
            layer_data={
                "title": "Test Design Layer",
                "description": "Test Description",
                "color": "#000000",
                "read_only": False,
            },
        )

        self.mutation = """
        mutation MyMutation($workspaceRequestId: Int!, $jsonSchema: JSONString!, $summaryFields: JSONString!, $orgId: Int!) {
          designLayerJsonSchema(
            dataInput: {workspaceRequestId: $workspaceRequestId, jsonSchema: $jsonSchema, summaryFields: $summaryFields, orgId: $orgId}
          ) {
            workspace {
              id
              name
              description
            }
          }
        }
        """

        # Sample JSON schema for testing
        self.json_schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string", "title": "Name"},
                "age": {"type": "integer", "title": "Age"},
                "address": {"type": "string", "title": "Address"},
            },
        }

        # Sample summary fields for testing
        self.summary_fields = ["name", "age"]

        self.variables = {
            "workspaceRequestId": self.workspace_request.id,
            "jsonSchema": json.dumps(self.json_schema),
            "summaryFields": json.dumps(self.summary_fields),
            "orgId": self.organization.id,
        }

    def test_query_authorization(self):
        """Test that unauthorized users cannot create a design layer JSON schema."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with an authorization error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_missing_required_fields(self):
        """Test that required fields are validated."""
        # Remove required fields
        variables_without_json_schema = self.variables.copy()
        del variables_without_json_schema["jsonSchema"]

        response = self.client.execute(
            self.mutation,
            variables=variables_without_json_schema,
            context=self.auth_request,
        )
        # Check if the query fails with a required field error
        self.assertIn("errors", response)
        self.assertIn("jsonschema", response["errors"][0]["message"].lower())

    def test_invalid_workspace_request_id(self):
        """Test that an invalid workspace request ID is handled properly."""
        # Use a non-existent workspace request ID
        invalid_variables = self.variables.copy()
        invalid_variables["workspaceRequestId"] = 99999

        response = self.client.execute(
            self.mutation, variables=invalid_variables, context=self.auth_request
        )
        # Check if the query fails with a workspace request error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"workspaceRequestId": "Dataset with 99999 not found"},
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_invalid_json_schema(self):
        """Test that an invalid JSON schema is handled properly."""
        # Create an invalid JSON schema
        invalid_variables = self.variables.copy()
        invalid_json_schema = {
            "type": "invalid_type",  # Invalid schema type
            "properties": {},
        }
        invalid_variables["jsonSchema"] = json.dumps(invalid_json_schema)

        response = self.client.execute(
            self.mutation, variables=invalid_variables, context=self.auth_request
        )
        # Check if the query fails with a JSON schema error
        self.assertIn("errors", response)
        self.assertIn("jsonschema", str(response["errors"][0]["extensions"]).lower())

    def test_other_user_workspace_request_regular_user(self):
        """Test that a regular user cannot create a JSON schema for another user's workspace request."""
        # Create a workspace request owned by another user
        other_user = UserFactory(is_superuser=False)
        other_workspace_request = WorkspaceRequestFactory(
            created_by=other_user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            request_type=RequestTypeChoices.DESIGN_LAYER,
            layer_data={
                "title": "Other User Design Layer",
                "description": "Other User Description",
                "color": "#000000",
                "read_only": False,
            },
        )

        # Try to create a JSON schema for the other user's workspace request
        other_variables = self.variables.copy()
        other_variables["workspaceRequestId"] = other_workspace_request.id

        response = self.client.execute(
            self.mutation, variables=other_variables, context=self.auth_request
        )
        # Check if the query fails with a permission error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "workspaceRequestId": f"Dataset with {other_workspace_request.id} not found"
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_other_user_workspace_request_superuser(self):
        """Test that a superuser can create a JSON schema for another user's workspace request."""
        # Create a workspace request owned by another user
        other_user = UserFactory(is_superuser=False)
        other_workspace_request = WorkspaceRequestFactory(
            created_by=other_user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            request_type=RequestTypeChoices.DESIGN_LAYER,
            layer_data={
                "title": "Other User Design Layer",
                "description": "Other User Description",
                "color": "#000000",
                "read_only": False,
            },
        )

        # Try to create a JSON schema for the other user's workspace request as a superuser
        other_variables = self.variables.copy()
        other_variables["workspaceRequestId"] = other_workspace_request.id

        response = self.client.execute(
            self.mutation,
            variables=other_variables,
            context=self.super_user_auth_request,
        )
        # The mutation should execute successfully
        self.assertNotIn("errors", response)

        # Verify the workspace was created
        data = response["data"]["designLayerJsonSchema"]["workspace"]
        workspace = Workspace.objects.get(id=data["id"])
        self.assertTrue(workspace)

        # Verify the layer was created
        self.assertTrue(Layer.objects.filter(workspace=workspace).exists())

        # Verify the workspace request is now finished
        other_workspace_request.refresh_from_db()
        self.assertEqual(
            other_workspace_request.status, WorkspaceRequestChoices.FINISHED
        )

        # Verify the layer is associated with the workspace request
        self.assertIsNotNone(other_workspace_request.layer)

    def test_successful_creation(self):
        """Test successful creation of a design layer JSON schema."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)

        # Verify the workspace was created
        data = response["data"]["designLayerJsonSchema"]["workspace"]
        workspace = Workspace.objects.get(id=data["id"])
        self.assertEqual(workspace.name, self.workspace_request.layer_data["title"])

        # Verify the layer was created
        layer = Layer.objects.get(workspace=workspace)
        self.assertEqual(layer.title, self.workspace_request.layer_data["title"])

        # Verify the workspace request is now finished
        self.workspace_request.refresh_from_db()
        self.assertEqual(
            self.workspace_request.status, WorkspaceRequestChoices.FINISHED
        )

        # Verify the layer is associated with the workspace request
        self.assertEqual(self.workspace_request.layer, layer)

    def test_json_schema_stored_correctly(self):
        """Test that JSON schema is stored correctly in layer and workspace request."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)

        # Verify JSON schema is stored in workspace request
        self.workspace_request.refresh_from_db()
        self.assertEqual(self.workspace_request.layer_data["json_schema"], self.json_schema)

        # Verify JSON schema is stored in layer
        workspace_id = response["data"]["designLayerJsonSchema"]["workspace"]["id"]
        workspace = Workspace.objects.get(id=workspace_id)
        layer = Layer.objects.get(workspace=workspace)
        self.assertEqual(layer.json_schema, self.json_schema)

    def test_summary_fields_stored_correctly(self):
        """Test that summary fields are stored correctly in layer data."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)

        # Verify summary fields are stored in layer
        workspace_id = response["data"]["designLayerJsonSchema"]["workspace"]["id"]
        workspace = Workspace.objects.get(id=workspace_id)
        layer = Layer.objects.get(workspace=workspace)
        self.assertEqual(layer.data["summary_fields"], self.summary_fields)

    def test_columns_extracted_from_json_schema(self):
        """Test that columns are correctly extracted from JSON schema properties."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)

        # Verify columns are extracted from JSON schema properties
        workspace_id = response["data"]["designLayerJsonSchema"]["workspace"]["id"]
        workspace = Workspace.objects.get(id=workspace_id)
        layer = Layer.objects.get(workspace=workspace)
        expected_columns = list(self.json_schema["properties"].keys())
        self.assertEqual(layer.data["columns"], expected_columns)

    def test_workspace_request_wrong_status(self):
        """Test error when workspace request is not in progress."""
        # Change workspace request status to finished
        self.workspace_request.status = WorkspaceRequestChoices.FINISHED
        self.workspace_request.save()

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertIn("errors", response)
        # The error message should contain "bad request" for wrong status
        self.assertIn("bad request", response["errors"][0]["message"].lower())

    def test_workspace_request_wrong_type(self):
        """Test error when workspace request has wrong type."""
        # Change workspace request type to upload file
        self.workspace_request.request_type = RequestTypeChoices.UPLOAD_FILE
        self.workspace_request.save()

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertIn("errors", response)
        # The error message should contain "bad request" for wrong type
        self.assertIn("bad request", response["errors"][0]["message"].lower())

    def test_workspace_request_no_layer_data(self):
        """Test error when workspace request has incomplete layer data."""
        # Create a new workspace request with incomplete layer_data (missing required fields like title)
        workspace_request_no_data = WorkspaceRequestFactory(
            created_by=self.user,
            organization=self.organization,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            request_type=RequestTypeChoices.DESIGN_LAYER,
            layer_data={"color": "#000000"},  # Missing title and other required fields
        )

        variables = self.variables.copy()
        variables["workspaceRequestId"] = workspace_request_no_data.id

        response = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", response)
        # The error message should contain "title" for missing title field
        self.assertIn("title", response["errors"][0]["message"].lower())

    def test_complex_json_schema(self):
        """Test with complex JSON schema containing nested objects and arrays."""
        complex_schema = {
            "type": "object",
            "properties": {
                "personal_info": {
                    "type": "object",
                    "properties": {
                        "first_name": {"type": "string"},
                        "last_name": {"type": "string"}
                    }
                },
                "contact": {
                    "type": "object",
                    "properties": {
                        "email": {"type": "string", "format": "email"},
                        "phone": {"type": "string"}
                    }
                },
                "tags": {
                    "type": "array",
                    "items": {"type": "string"}
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": True
                }
            },
            "required": ["personal_info", "contact"]
        }

        variables = self.variables.copy()
        variables["jsonSchema"] = json.dumps(complex_schema)

        response = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)

        # Verify complex schema is stored correctly
        workspace_id = response["data"]["designLayerJsonSchema"]["workspace"]["id"]
        workspace = Workspace.objects.get(id=workspace_id)
        layer = Layer.objects.get(workspace=workspace)
        self.assertEqual(layer.json_schema, complex_schema)

        # Verify top-level properties are extracted as columns
        expected_columns = ["personal_info", "contact", "tags", "metadata"]
        self.assertEqual(layer.data["columns"], expected_columns)

    def test_empty_json_schema_properties(self):
        """Test with JSON schema that has empty properties."""
        empty_properties_schema = {
            "type": "object",
            "properties": {}
        }

        variables = self.variables.copy()
        variables["jsonSchema"] = json.dumps(empty_properties_schema)

        response = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)

        # Verify empty columns list
        workspace_id = response["data"]["designLayerJsonSchema"]["workspace"]["id"]
        workspace = Workspace.objects.get(id=workspace_id)
        layer = Layer.objects.get(workspace=workspace)
        self.assertEqual(layer.data["columns"], [])

    def test_malformed_json_in_variables(self):
        """Test error handling for malformed JSON in variables."""
        variables = self.variables.copy()
        variables["jsonSchema"] = "invalid json string"

        response = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", response)

    def test_workspace_type_set_correctly(self):
        """Test that workspace type is set to DESIGN_LAYER."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)

        workspace_id = response["data"]["designLayerJsonSchema"]["workspace"]["id"]
        workspace = Workspace.objects.get(id=workspace_id)
        self.assertEqual(workspace.workspace_type, RequestTypeChoices.DESIGN_LAYER)

    def test_workspace_owner_set_correctly(self):
        """Test that workspace owner is set to the request creator."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)

        workspace_id = response["data"]["designLayerJsonSchema"]["workspace"]["id"]
        workspace = Workspace.objects.get(id=workspace_id)
        self.assertEqual(workspace.owner, self.workspace_request.created_by)

    def test_workspace_organization_set_correctly(self):
        """Test that workspace organization is set correctly."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)

        workspace_id = response["data"]["designLayerJsonSchema"]["workspace"]["id"]
        workspace = Workspace.objects.get(id=workspace_id)
        self.assertEqual(workspace.organization, self.workspace_request.organization)

    def test_layer_properties_set_from_workspace_request(self):
        """Test that layer properties are correctly set from workspace request."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)

        workspace_id = response["data"]["designLayerJsonSchema"]["workspace"]["id"]
        workspace = Workspace.objects.get(id=workspace_id)
        layer = Layer.objects.get(workspace=workspace)

        # Verify layer properties match workspace request layer_data
        self.assertEqual(layer.title, self.workspace_request.layer_data["title"])
        self.assertEqual(layer.description, self.workspace_request.layer_data["description"])
        self.assertEqual(layer.color, self.workspace_request.layer_data["color"])
        self.assertEqual(layer.read_only, self.workspace_request.layer_data["read_only"])

    def test_invalid_organization_id(self):
        """Test error when organization ID doesn't match workspace request."""
        # Create another organization
        from common.tests.factories import OrganizationFactory
        other_org = OrganizationFactory()

        variables = self.variables.copy()
        variables["orgId"] = other_org.id

        response = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", response)
        # The error message should contain "unauthorized" for wrong organization
        self.assertIn("unauthorized", response["errors"][0]["message"].lower())

    def test_json_schema_with_definitions(self):
        """Test JSON schema with definitions and references."""
        schema_with_definitions = {
            "type": "object",
            "definitions": {
                "address": {
                    "type": "object",
                    "properties": {
                        "street": {"type": "string"},
                        "city": {"type": "string"},
                        "zipcode": {"type": "string"}
                    }
                }
            },
            "properties": {
                "name": {"type": "string"},
                "home_address": {"$ref": "#/definitions/address"},
                "work_address": {"$ref": "#/definitions/address"}
            }
        }

        variables = self.variables.copy()
        variables["jsonSchema"] = json.dumps(schema_with_definitions)

        response = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)

        # Verify schema with definitions is stored correctly
        workspace_id = response["data"]["designLayerJsonSchema"]["workspace"]["id"]
        workspace = Workspace.objects.get(id=workspace_id)
        layer = Layer.objects.get(workspace=workspace)
        self.assertEqual(layer.json_schema, schema_with_definitions)

    def test_empty_summary_fields(self):
        """Test with empty summary fields array."""
        variables = self.variables.copy()
        variables["summaryFields"] = json.dumps([])

        response = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)

        # Verify empty summary fields are stored
        workspace_id = response["data"]["designLayerJsonSchema"]["workspace"]["id"]
        workspace = Workspace.objects.get(id=workspace_id)
        layer = Layer.objects.get(workspace=workspace)
        self.assertEqual(layer.data["summary_fields"], [])

    def test_dataset_association_preserved(self):
        """Test that dataset association is preserved in the created layer."""
        # Add a dataset to the workspace request
        from common.tests.factories import DatasetFactory
        dataset = DatasetFactory()
        self.workspace_request.dataset = dataset
        self.workspace_request.save()

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)

        # Verify dataset association
        workspace_id = response["data"]["designLayerJsonSchema"]["workspace"]["id"]
        workspace = Workspace.objects.get(id=workspace_id)
        layer = Layer.objects.get(workspace=workspace)
        self.assertEqual(layer.dataset, dataset)
